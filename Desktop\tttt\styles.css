/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

.map-container {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    overflow: hidden;
}

#africa-map {
    width: 100%;
    height: auto;
    max-height: 80vh;
}

/* Country styles */
.country {
    fill: #4a90e2;
    stroke: #2c5aa0;
    stroke-width: 1;
    cursor: pointer;
    transition: all 0.3s ease;
}

.country:hover {
    fill: #ff6b6b;
    stroke: #e55656;
    stroke-width: 2;
    transform-origin: center;
}

.country:active {
    fill: #ffa726;
}

.country.selected {
    fill: #4caf50;
    stroke: #388e3c;
    stroke-width: 2;
}

/* Tooltip styles */
.tooltip {
    position: absolute;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1000;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
}

.tooltip.show {
    opacity: 1;
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    header p {
        font-size: 1rem;
    }
    
    .map-container {
        padding: 15px;
    }
    
    .tooltip {
        font-size: 12px;
        padding: 6px 10px;
    }
}

@media (max-width: 480px) {
    header h1 {
        font-size: 1.5rem;
    }
    
    .map-container {
        padding: 10px;
    }
}

/* Loading animation */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    color: #666;
    font-size: 1.2rem;
}

.loading::after {
    content: '';
    width: 20px;
    height: 20px;
    border: 2px solid #ddd;
    border-top: 2px solid #4a90e2;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
