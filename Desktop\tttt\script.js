// African countries data with more accurate SVG paths
const africanCountries = {
    'Algeria': 'M150,50 L450,30 L480,80 L460,150 L420,200 L380,220 L320,240 L280,220 L240,180 L200,140 L170,100 Z',
    'Libya': 'M450,30 L580,40 L620,80 L610,140 L580,180 L540,200 L480,190 L460,150 L480,80 Z',
    'Egypt': 'M580,40 L680,35 L720,70 L710,130 L690,170 L650,190 L610,180 L610,140 L620,80 Z',
    'Sudan': 'M610,180 L690,190 L720,130 L750,170 L780,220 L760,280 L720,320 L680,340 L640,320 L620,280 L600,240 L610,180 Z',
    'South Sudan': 'M620,280 L680,300 L700,320 L690,360 L660,380 L620,370 L600,340 L600,300 L620,280 Z',
    'Chad': 'M540,200 L610,180 L600,240 L580,280 L560,320 L520,340 L480,320 L500,280 L520,240 L540,200 Z',
    'Niger': 'M380,220 L500,200 L540,220 L520,260 L480,300 L440,320 L400,300 L380,260 L380,220 Z',
    'Mali': 'M240,180 L380,200 L380,240 L360,280 L320,300 L280,280 L260,240 L240,200 L240,180 Z',
    'Mauritania': 'M170,100 L240,120 L260,160 L240,200 L200,220 L160,200 L140,160 L150,120 L170,100 Z',
    'Morocco': 'M80,40 L170,60 L200,100 L170,140 L140,120 L110,80 L80,60 L80,40 Z',
    'Tunisia': 'M460,30 L500,25 L520,50 L510,80 L490,100 L470,80 L460,50 L460,30 Z',
    'Senegal': 'M120,160 L160,180 L150,200 L130,220 L110,200 L90,180 L100,160 L120,160 Z',
    'Gambia': 'M120,180 L150,185 L145,195 L115,190 L120,180 Z',
    'Guinea-Bissau': 'M100,200 L130,210 L125,220 L95,215 L100,200 Z',
    'Guinea': 'M100,220 L140,240 L160,260 L140,280 L120,270 L100,250 L100,220 Z',
    'Sierra Leone': 'M120,270 L140,280 L135,300 L115,295 L120,270 Z',
    'Liberia': 'M115,295 L135,300 L140,320 L120,325 L100,315 L115,295 Z',
    'Ivory Coast': 'M140,280 L180,300 L200,320 L180,340 L160,330 L140,320 L140,280 Z',
    'Ghana': 'M180,300 L220,320 L215,360 L195,355 L180,340 L180,300 Z',
    'Burkina Faso': 'M220,260 L280,280 L260,320 L220,320 L200,300 L220,260 Z',
    'Togo': 'M215,360 L230,365 L225,400 L210,395 L215,360 Z',
    'Benin': 'M225,400 L240,405 L235,440 L220,435 L225,400 Z',
    'Nigeria': 'M240,320 L320,340 L340,380 L320,420 L280,440 L240,420 L220,380 L240,320 Z',
    'Cameroon': 'M320,340 L380,360 L400,400 L380,440 L360,460 L320,450 L300,420 L320,340 Z',
    'Equatorial Guinea': 'M300,420 L320,425 L315,435 L295,430 L300,420 Z',
    'Gabon': 'M300,430 L340,450 L335,480 L315,475 L300,460 L300,430 Z',
    'Republic of the Congo': 'M340,450 L380,460 L400,500 L380,540 L360,530 L340,500 L340,450 Z',
    'Democratic Republic of the Congo': 'M380,440 L480,460 L520,500 L540,560 L500,620 L440,640 L380,620 L360,580 L360,530 L380,500 L400,460 L380,440 Z',
    'Central African Republic': 'M380,360 L480,380 L500,420 L480,460 L440,450 L400,430 L380,400 L380,360 Z',
    'Ethiopia': 'M640,320 L740,340 L780,380 L760,440 L720,460 L680,440 L660,400 L640,360 L640,320 Z',
    'Eritrea': 'M690,170 L750,190 L770,230 L750,270 L730,250 L710,230 L690,210 L690,170 Z',
    'Djibouti': 'M750,270 L770,275 L765,290 L745,285 L750,270 Z',
    'Somalia': 'M740,340 L820,360 L850,420 L830,480 L790,500 L760,460 L740,420 L740,340 Z',
    'Kenya': 'M680,440 L760,460 L780,520 L760,580 L720,600 L680,580 L660,540 L680,500 L680,440 Z',
    'Uganda': 'M640,440 L680,460 L680,500 L660,520 L620,500 L600,460 L620,440 L640,440 Z',
    'Rwanda': 'M620,500 L640,505 L635,520 L615,515 L620,500 Z',
    'Burundi': 'M615,515 L635,520 L630,535 L610,530 L615,515 Z',
    'Tanzania': 'M660,520 L760,540 L780,600 L760,660 L720,680 L680,660 L660,620 L640,580 L660,540 L660,520 Z',
    'Angola': 'M440,640 L540,660 L560,720 L540,780 L500,800 L460,780 L420,740 L420,700 L440,660 L440,640 Z',
    'Zambia': 'M540,660 L620,680 L640,740 L620,800 L580,820 L540,800 L520,760 L540,720 L540,660 Z',
    'Malawi': 'M640,680 L660,685 L655,740 L635,735 L640,700 L640,680 Z',
    'Mozambique': 'M655,740 L720,760 L740,820 L720,880 L680,900 L640,880 L620,840 L640,800 L655,760 L655,740 Z',
    'Zimbabwe': 'M580,800 L640,820 L620,860 L580,880 L540,860 L520,820 L540,800 L580,800 Z',
    'Botswana': 'M520,820 L580,840 L560,900 L520,920 L480,900 L460,860 L480,820 L520,820 Z',
    'Namibia': 'M420,740 L500,760 L480,820 L460,860 L420,880 L380,860 L360,820 L380,780 L420,760 L420,740 Z',
    'South Africa': 'M460,860 L560,880 L580,940 L560,980 L500,1000 L440,980 L400,940 L420,900 L460,880 L460,860 Z',
    'Lesotho': 'M500,940 L520,945 L515,960 L495,955 L500,940 Z',
    'Eswatini': 'M560,900 L580,905 L575,920 L555,915 L560,900 Z',
    'Madagascar': 'M820,680 L840,685 L860,740 L855,820 L840,880 L820,900 L800,880 L795,820 L800,760 L820,700 L820,680 Z',
    'Comoros': 'M780,660 L790,665 L785,675 L775,670 L780,660 Z',
    'Mauritius': 'M880,780 L890,785 L885,795 L875,790 L880,780 Z',
    'Seychelles': 'M800,580 L810,585 L805,595 L795,590 L800,580 Z',
    'Cape Verde': 'M30,180 L50,185 L45,200 L25,195 L30,180 Z',
    'São Tomé and Príncipe': 'M280,440 L290,445 L285,455 L275,450 L280,440 Z'
};

// Initialize the map
document.addEventListener('DOMContentLoaded', function() {
    const svg = document.getElementById('africa-map');
    const tooltip = document.getElementById('tooltip');
    const countryName = document.getElementById('country-name');
    
    // Create country elements
    Object.entries(africanCountries).forEach(([name, path]) => {
        const countryElement = document.createElementNS('http://www.w3.org/2000/svg', 'path');
        countryElement.setAttribute('d', path);
        countryElement.setAttribute('class', 'country');
        countryElement.setAttribute('data-country', name);
        
        // Add event listeners
        countryElement.addEventListener('click', function(e) {
            showCountryInfo(name, e);
        });
        
        countryElement.addEventListener('mouseenter', function(e) {
            showTooltip(name, e);
        });
        
        countryElement.addEventListener('mouseleave', function() {
            hideTooltip();
        });
        
        countryElement.addEventListener('mousemove', function(e) {
            updateTooltipPosition(e);
        });
        
        svg.appendChild(countryElement);
    });
    
    function showCountryInfo(country, event) {
        // Create a more prominent display for clicked countries
        const clickedCountry = event.target;

        // Remove previous selections
        document.querySelectorAll('.country.selected').forEach(el => {
            el.classList.remove('selected');
        });

        // Add selection class
        clickedCountry.classList.add('selected');

        // Show country name prominently in the header
        const header = document.querySelector('header p');
        header.textContent = `Selected: ${country}`;
        header.style.fontSize = '1.3rem';
        header.style.fontWeight = 'bold';

        // Reset after 3 seconds
        setTimeout(() => {
            header.textContent = 'Click on any country to see its name';
            header.style.fontSize = '1.1rem';
            header.style.fontWeight = 'normal';
            clickedCountry.classList.remove('selected');
        }, 3000);
    }
    
    function showTooltip(country, event) {
        countryName.textContent = country;
        tooltip.classList.add('show');
        updateTooltipPosition(event);
    }
    
    function hideTooltip() {
        tooltip.classList.remove('show');
    }
    
    function updateTooltipPosition(event) {
        const x = event.clientX;
        const y = event.clientY;
        
        tooltip.style.left = (x + 10) + 'px';
        tooltip.style.top = (y - 10) + 'px';
    }
});
