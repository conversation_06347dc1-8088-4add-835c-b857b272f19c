<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Map of Africa</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>Interactive Map of Africa</h1>
            <p>Click on any country to see its name</p>
        </header>
        
        <div class="map-container">
            <svg id="africa-map" viewBox="0 0 1000 1000" xmlns="http://www.w3.org/2000/svg">
                <!-- Countries will be added here -->
            </svg>
        </div>
        
        <!-- Tooltip for country names -->
        <div id="tooltip" class="tooltip">
            <span id="country-name"></span>
        </div>
    </div>
    
    <script src="script.js"></script>
</body>
</html>
